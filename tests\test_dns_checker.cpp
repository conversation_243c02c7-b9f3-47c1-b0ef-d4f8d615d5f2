#include <iostream>
#include <cassert>
#include <string>
#include <functional>
#include "../src/dns_health_checker.h"

using namespace dns_health_checker;

// Simple test framework
class TestRunner
{
public:
    static void run_test(const std::string &test_name, std::function<void()> test_func)
    {
        try
        {
            test_func();
            std::cout << "[PASS] " << test_name << std::endl;
            passed_++;
        }
        catch (const std::exception &e)
        {
            std::cout << "[FAIL] " << test_name << " - " << e.what() << std::endl;
            failed_++;
        }
        catch (...)
        {
            std::cout << "[FAIL] " << test_name << " - Unknown exception" << std::endl;
            failed_++;
        }
        total_++;
    }

    static void print_summary()
    {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Total: " << total_ << std::endl;
        std::cout << "Passed: " << passed_ << std::endl;
        std::cout << "Failed: " << failed_ << std::endl;
        std::cout << "Success Rate: " << (total_ > 0 ? (passed_ * 100.0 / total_) : 0) << "%" << std::endl;
    }

    static int get_exit_code()
    {
        return failed_ > 0 ? 1 : 0;
    }

private:
    static int total_;
    static int passed_;
    static int failed_;
};

int TestRunner::total_ = 0;
int TestRunner::passed_ = 0;
int TestRunner::failed_ = 0;

// Test utility functions
void test_dns_utils()
{
    TestRunner::run_test("dns_utils::resultToString", []()
                         {
        assert(dns_utils::resultToString(CheckResult::PASS) == "PASS");
        assert(dns_utils::resultToString(CheckResult::FAIL) == "FAIL");
        assert(dns_utils::resultToString(CheckResult::WARNING) == "WARNING");
        assert(dns_utils::resultToString(CheckResult::NOT_APPLICABLE) == "NOT_APPLICABLE"); });

    TestRunner::run_test("dns_utils::isValidIPv4", []()
                         {
        assert(dns_utils::isValidIPv4("***********") == true);
        assert(dns_utils::isValidIPv4("*******") == true);
        assert(dns_utils::isValidIPv4("256.1.1.1") == false);
        assert(dns_utils::isValidIPv4("192.168.1") == false);
        assert(dns_utils::isValidIPv4("not.an.ip") == false); });

    TestRunner::run_test("dns_utils::extractDomainFromEmail", []()
                         {
        assert(dns_utils::extractDomainFromEmail("<EMAIL>") == "example.com");
        assert(dns_utils::extractDomainFromEmail("<EMAIL>") == "subdomain.example.org");
        assert(dns_utils::extractDomainFromEmail("invalid-email") == ""); });
}

// Test DNS Health Checker basic functionality
void test_dns_health_checker_basic()
{
    TestRunner::run_test("DNSHealthChecker::constructor", []()
                         {
                             DNSHealthChecker checker;
                             // Constructor should not throw
                         });

    TestRunner::run_test("DNSHealthChecker::isValidDomain", []()
                         {
        DNSHealthChecker checker;
        assert(checker.isValidDomain("example.com") == true);
        assert(checker.isValidDomain("subdomain.example.com") == true);
        assert(checker.isValidDomain("") == false);
        assert(checker.isValidDomain("invalid..domain") == false);
        assert(checker.isValidDomain(".invalid") == false); });
}

// Test CheckInfo structure
void test_check_info()
{
    TestRunner::run_test("CheckInfo::constructor", []()
                         {
        CheckInfo info("Test Check", CheckResult::PASS, "Test details");
        assert(info.name == "Test Check");
        assert(info.result == CheckResult::PASS);
        assert(info.details == "Test details");
        assert(info.recommendations.empty()); });

    TestRunner::run_test("CheckInfo::recommendations", []()
                         {
        CheckInfo info("Test Check", CheckResult::FAIL);
        info.recommendations.push_back("Fix this issue");
        info.recommendations.push_back("Also fix that issue");
        assert(info.recommendations.size() == 2);
        assert(info.recommendations[0] == "Fix this issue"); });
}

// Test HealthReport structure
void test_health_report()
{
    TestRunner::run_test("HealthReport::constructor", []()
                         {
        HealthReport report("example.com");
        assert(report.domain == "example.com");
        assert(report.overall_healthy == true);
        assert(report.checks.empty()); });

    TestRunner::run_test("HealthReport::add_checks", []()
                         {
        HealthReport report("example.com");
        report.checks.emplace_back("SPF Check", CheckResult::PASS, "SPF record found");
        report.checks.emplace_back("DMARC Check", CheckResult::FAIL, "No DMARC record");
        
        assert(report.checks.size() == 2);
        assert(report.checks[0].name == "SPF Check");
        assert(report.checks[1].result == CheckResult::FAIL); });
}

// Integration tests (these may require network access)
void test_integration()
{
    TestRunner::run_test("DNSHealthChecker::checkDomain_google", []()
                         {
        try {
            DNSHealthChecker checker;
            // Test with a well-known domain that should have good DNS configuration
            auto report = checker.checkDomain("google.com");
            assert(report.domain == "google.com");
            assert(!report.checks.empty());
            // Note: Without ldns library, checks will return appropriate failure states
            // This test mainly verifies the structure works correctly
        } catch (const std::exception& e) {
            // Expected when DNS libraries are not available
            std::cout << "Note: DNS functionality not available - " << e.what() << std::endl;
        } });

    TestRunner::run_test("DNSHealthChecker::checkLocalMachine", []()
                         {
        try {
            DNSHealthChecker checker;
            auto report = checker.checkLocalMachine();
            assert(!report.domain.empty());
            assert(!report.checks.empty());
        } catch (const std::exception& e) {
            // Expected when DNS libraries are not available
            std::cout << "Note: DNS functionality not available - " << e.what() << std::endl;
        } });
}

int main()
{
    std::cout << "Running DNS Health Checker Tests...\n"
              << std::endl;

    // Run all test suites
    test_dns_utils();
    test_dns_health_checker_basic();
    test_check_info();
    test_health_report();

    // Run integration tests (comment out if no network access)
    std::cout << "\nRunning integration tests (requires network access)..." << std::endl;
    try
    {
        test_integration();
    }
    catch (...)
    {
        std::cout << "Integration tests skipped (network issues)" << std::endl;
    }

    TestRunner::print_summary();
    return TestRunner::get_exit_code();
}
