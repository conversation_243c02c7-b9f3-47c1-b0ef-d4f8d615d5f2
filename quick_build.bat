@echo off
echo ========================================
echo DNS Health Checker - Quick Build Script
echo ========================================
echo.

REM Create build directory
if not exist "build" mkdir build

echo Attempting to build DNS Health Checker...
echo.

REM Method 1: Try Visual Studio compiler
echo [1/4] Trying Visual Studio compiler (cl)...
where cl >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Found Visual Studio compiler!
    cl /EHsc /std:c++17 /I. src\main.cpp src\dns_health_checker.cpp src\dns_checker.cpp src\report_generator.cpp /Fe:build\dns_health_checker.exe >build\vs_build.log 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✅ SUCCESS: Built with Visual Studio compiler!
        goto :test_executable
    ) else (
        echo ❌ Visual Studio compilation failed. Check build\vs_build.log
    )
) else (
    echo Visual Studio compiler not found.
)

echo.

REM Method 2: Try MinGW/GCC
echo [2/4] Trying MinGW/GCC compiler (g++)...
where g++ >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Found GCC compiler!
    g++ -std=c++17 -I. -o build\dns_health_checker.exe src\main.cpp src\dns_health_checker.cpp src\dns_checker.cpp src\report_generator.cpp >build\gcc_build.log 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✅ SUCCESS: Built with GCC compiler!
        goto :test_executable
    ) else (
        echo ❌ GCC compilation failed. Check build\gcc_build.log
    )
) else (
    echo GCC compiler not found.
)

echo.

REM Method 3: Try Clang
echo [3/4] Trying Clang compiler...
where clang++ >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Found Clang compiler!
    clang++ -std=c++17 -I. -o build\dns_health_checker.exe src\main.cpp src\dns_health_checker.cpp src\dns_checker.cpp src\report_generator.cpp >build\clang_build.log 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✅ SUCCESS: Built with Clang compiler!
        goto :test_executable
    ) else (
        echo ❌ Clang compilation failed. Check build\clang_build.log
    )
) else (
    echo Clang compiler not found.
)

echo.

REM Method 4: Check for CMake
echo [4/4] Trying CMake build...
where cmake >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Found CMake!
    cd build
    cmake .. >cmake_config.log 2>&1
    if %ERRORLEVEL% EQU 0 (
        cmake --build . >cmake_build.log 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo ✅ SUCCESS: Built with CMake!
            cd ..
            goto :test_executable
        ) else (
            echo ❌ CMake build failed. Check build\cmake_build.log
            cd ..
        )
    ) else (
        echo ❌ CMake configuration failed. Check build\cmake_config.log
        cd ..
    )
) else (
    echo CMake not found.
)

echo.
echo ❌ BUILD FAILED: No suitable compiler found.
echo.
echo To fix this, you need to install one of the following:
echo.
echo 1. Visual Studio Community (recommended for Windows)
echo    Download: https://visualstudio.microsoft.com/vs/community/
echo    Select "Desktop development with C++" during installation
echo.
echo 2. MinGW-w64 (GCC for Windows)
echo    Download: https://www.mingw-w64.org/downloads/
echo    Or install via MSYS2: https://www.msys2.org/
echo.
echo 3. LLVM/Clang
echo    Download: https://llvm.org/builds/
echo.
echo After installation, run this script again.
echo.
goto :end

:test_executable
echo.
echo ========================================
echo Testing the executable...
echo ========================================
echo.

if exist "build\dns_health_checker.exe" (
    echo ✅ Executable created successfully!
    echo.
    echo Testing help command...
    build\dns_health_checker.exe --help
    echo.
    echo ========================================
    echo.
    echo 🎉 SUCCESS! DNS Health Checker is ready to use!
    echo.
    echo Try these commands:
    echo   build\dns_health_checker.exe -d google.com
    echo   build\dns_health_checker.exe -l
    echo   build\dns_health_checker.exe -d example.com -o my_report.pdf
    echo.
    echo Note: Without DNS libraries, checks will show "NOT_APPLICABLE"
    echo This is expected behavior for the basic build.
    echo.
    echo For full functionality, see run_project.md for dependency installation.
    echo.
) else (
    echo ❌ Executable not found after build.
    echo Check the build logs in the build\ directory.
)

:end
echo.
echo Build process completed.
pause
