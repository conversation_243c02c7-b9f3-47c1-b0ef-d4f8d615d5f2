@echo off
echo Building DNS Health Checker...

REM Create build directory if it doesn't exist
if not exist "build" mkdir build

REM Try to compile with g++ (MinGW)
echo Attempting to compile with g++...
g++ -std=c++17 -I. -o build\dns_health_checker.exe src\main.cpp src\dns_health_checker.cpp src\dns_checker.cpp src\report_generator.cpp 2>build\compile_errors.txt

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Executable created at build\dns_health_checker.exe
    echo.
    echo Note: This build does not include DNS resolution or PDF generation capabilities.
    echo For full functionality, install ldns and libharu libraries and use CMake.
    echo.
    echo Testing basic functionality...
    build\dns_health_checker.exe --help
) else (
    echo Build failed. Checking for Visual Studio compiler...
    
    REM Try with Visual Studio compiler
    cl /EHsc /std:c++17 /I. src\main.cpp src\dns_health_checker.cpp src\dns_checker.cpp src\report_generator.cpp /Fe:build\dns_health_checker.exe 2>build\compile_errors_vs.txt
    
    if %ERRORLEVEL% EQU 0 (
        echo Build successful with Visual Studio compiler!
        echo Executable created at build\dns_health_checker.exe
        echo.
        echo Testing basic functionality...
        build\dns_health_checker.exe --help
    ) else (
        echo Build failed with both compilers.
        echo Check build\compile_errors.txt and build\compile_errors_vs.txt for details.
        echo.
        echo To build successfully, you need:
        echo 1. A C++17 compatible compiler (g++ or Visual Studio)
        echo 2. Optional: ldns library for DNS functionality
        echo 3. Optional: libharu library for PDF generation
    )
)

pause
