<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS Health Checker - Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .check-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: white;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }
        .pass { background-color: #27ae60; }
        .fail { background-color: #e74c3c; }
        .warning { background-color: #f39c12; }
        .not-applicable { background-color: #95a5a6; }
        .input-section {
            margin: 20px 0;
        }
        input[type="text"] {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        .code-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .project-structure {
            background-color: #34495e;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 DNS Health Checker - Interactive Demo</h1>
        
        <div class="demo-section">
            <h2>Project Overview</h2>
            <p>This is a comprehensive C++ application that performs DNS and domain health checks, generating detailed reports to identify security vulnerabilities and misconfigurations.</p>
            
            <div class="feature-list">
                <div class="feature-card">
                    <h3>✅ DNS Security Checks</h3>
                    <ul>
                        <li>SPF Record Verification</li>
                        <li>DKIM Authentication</li>
                        <li>DMARC Policy Validation</li>
                        <li>MX Record Analysis</li>
                        <li>DNSSEC Implementation</li>
                        <li>TTL Optimization</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h3>📊 Reporting Features</h3>
                    <ul>
                        <li>PDF Report Generation</li>
                        <li>Text Report Fallback</li>
                        <li>Detailed Recommendations</li>
                        <li>Pass/Fail Status</li>
                        <li>Timestamped Results</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h3>🛠️ Technical Features</h3>
                    <ul>
                        <li>Cross-platform C++17</li>
                        <li>CMake Build System</li>
                        <li>Unit Testing Framework</li>
                        <li>Graceful Dependency Handling</li>
                        <li>Command-line Interface</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🏗️ Project Structure</h2>
            <div class="project-structure">
dns-health-checker/
├── CMakeLists.txt              # Cross-platform build configuration
├── README.md                   # Comprehensive documentation  
├── LICENSE                     # MIT license
├── build.bat                   # Windows build script
├── quick_build.bat            # Automated build script
├── run_project.md             # Detailed run instructions
├── src/
│   ├── main.cpp               # Command-line interface
│   ├── dns_health_checker.h   # Main header file
│   ├── dns_health_checker.cpp # Core implementation
│   ├── dns_checker.cpp        # DNS check functions
│   └── report_generator.cpp   # PDF/text report generation
├── tests/
│   └── test_dns_checker.cpp   # Unit test suite
└── examples/
    ├── sample_config.json      # Configuration example
    └── usage_examples.sh       # Usage demonstration
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 Simulated DNS Health Check</h2>
            <div class="input-section">
                <input type="text" id="domainInput" placeholder="Enter domain (e.g., google.com)" value="google.com">
                <button onclick="runHealthCheck()">Run Health Check</button>
            </div>
            <div id="results"></div>
        </div>

        <div class="demo-section">
            <h2>💻 Command Line Usage</h2>
            <div class="code-block">
# Basic domain check
dns_health_checker.exe -d google.com

# Check with custom output
dns_health_checker.exe -d example.com -o my_report.pdf

# Local machine check  
dns_health_checker.exe -l

# Scheduled checks (every 60 minutes)
dns_health_checker.exe -d example.com -s 60

# Show help
dns_health_checker.exe --help
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 How to Run the Real Project</h2>
            <ol>
                <li><strong>Install a C++ Compiler:</strong>
                    <ul>
                        <li>Visual Studio Community (recommended for Windows)</li>
                        <li>MinGW-w64 (GCC for Windows)</li>
                        <li>Clang/LLVM</li>
                    </ul>
                </li>
                <li><strong>Build the Project:</strong>
                    <div class="code-block">
# Run the automated build script
quick_build.bat

# Or manually with Visual Studio
cl /EHsc /std:c++17 /I. src\*.cpp /Fe:dns_health_checker.exe
                    </div>
                </li>
                <li><strong>Run the Application:</strong>
                    <div class="code-block">
dns_health_checker.exe -d google.com
                    </div>
                </li>
            </ol>
            <p><strong>Note:</strong> The application works without DNS libraries (shows "NOT_APPLICABLE" status) and can be enhanced with ldns and libharu for full functionality.</p>
        </div>
    </div>

    <script>
        function runHealthCheck() {
            const domain = document.getElementById('domainInput').value || 'example.com';
            const resultsDiv = document.getElementById('results');
            
            // Simulate the DNS health check results
            const checks = [
                { name: 'SPF Record Check', status: 'not-applicable', details: 'DNS functionality not available - install ldns library for SPF checking' },
                { name: 'DKIM Record Check', status: 'not-applicable', details: 'DNS functionality not available - install ldns library for DKIM checking' },
                { name: 'DMARC Record Check', status: 'not-applicable', details: 'DNS functionality not available - install ldns library for DMARC checking' },
                { name: 'MX Record Check', status: 'not-applicable', details: 'DNS functionality not available - install ldns library for MX checking' },
                { name: 'PTR/Reverse DNS Check', status: 'not-applicable', details: 'PTR check not applicable for domain queries' },
                { name: 'DNSSEC Check', status: 'not-applicable', details: 'DNS functionality not available - install ldns library for DNSSEC checking' },
                { name: 'TTL Check', status: 'not-applicable', details: 'DNS functionality not available - install ldns library for TTL checking' }
            ];

            let html = `<h3>DNS Health Check Results for: ${domain}</h3>`;
            html += `<p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>`;
            html += `<p><strong>Overall Status:</strong> <span class="status not-applicable">ISSUES FOUND</span></p>`;
            
            checks.forEach(check => {
                html += `
                    <div class="check-result">
                        <div>
                            <strong>${check.name}</strong><br>
                            <small>${check.details}</small>
                        </div>
                        <span class="status ${check.status}">${check.status.toUpperCase().replace('-', '_')}</span>
                    </div>
                `;
            });

            html += `<p><strong>Note:</strong> This simulates the output you'd get when running the actual C++ application without DNS libraries installed. Install ldns and libharu libraries for full functionality.</p>`;
            
            resultsDiv.innerHTML = html;
        }

        // Run initial demo
        window.onload = function() {
            runHealthCheck();
        };
    </script>
</body>
</html>
