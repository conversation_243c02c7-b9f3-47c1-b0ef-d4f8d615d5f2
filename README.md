# DNS Health Checker

A comprehensive cross-platform C++ application that performs DNS and domain health checks, generating detailed PDF reports to identify security vulnerabilities and misconfigurations.

## Features

### Domain & DNS Health Checks

- **SPF** (Sender Policy Framework) record verification
- **DKIM** (DomainKeys Identified Mail) record verification
- **DMARC** (Domain-based Message Authentication, Reporting & Conformance) record verification
- **MX** (Mail Exchange) record validation
- **PTR/Reverse DNS** lookup verification
- **DNSSEC** (Domain Name System Security Extensions) availability checking
- **TTL** (Time To Live) evaluation for optimal caching

### Reporting

- Generate comprehensive PDF reports with detailed findings
- Include pass/fail status for each check
- Provide specific recommendations for fixing issues
- Support timestamped reports for scheduled scans

### Operational Modes

- One-time scan mode for immediate results
- Scheduled scan mode for periodic monitoring
- Support for both external domain and local machine checks

## Dependencies

- C++17 compatible compiler
- CMake 3.10+
- ldns library for DNS operations
- libharu (hpdf) for PDF generation
- Standard networking libraries

## Installation

### Ubuntu/Debian

```bash
sudo apt-get update
sudo apt-get install build-essential cmake libldns-dev libhpdf-dev
```

### CentOS/RHEL/Fedora

```bash
sudo yum install gcc-c++ cmake ldns-devel libharu-devel
# or for newer versions:
sudo dnf install gcc-c++ cmake ldns-devel libharu-devel
```

### macOS

```bash
brew install cmake ldns libharu
```

### Windows

Install dependencies using vcpkg or manually build from source.

## Build Instructions

### Option 1: Using CMake (Recommended for Linux/macOS)

```bash
# Clone repository
git clone https://github.com/yourusername/dns-health-checker.git
cd dns-health-checker

# Install dependencies first (see Installation section above)

# Build with CMake
mkdir build
cd build
cmake ..
make

# Run the application
./bin/dns_health_checker -d example.com
```

### Option 2: Using the Build Script (Windows)

```batch
# Clone repository and navigate to directory
git clone https://github.com/yourusername/dns-health-checker.git
cd dns-health-checker

# Run the build script
build.bat
```

### Option 3: Manual Compilation

If you don't have CMake or the build script doesn't work:

```bash
# Linux/macOS with dependencies
g++ -std=c++17 -DHAVE_LDNS=1 -DHAVE_HPDF=1 -I. -o dns_health_checker \
    src/main.cpp src/dns_health_checker.cpp src/dns_checker.cpp src/report_generator.cpp \
    -lldns -lhpdf

# Linux/macOS without dependencies (limited functionality)
g++ -std=c++17 -I. -o dns_health_checker \
    src/main.cpp src/dns_health_checker.cpp src/dns_checker.cpp src/report_generator.cpp

# Windows with Visual Studio
cl /EHsc /std:c++17 /I. src\main.cpp src\dns_health_checker.cpp src\dns_checker.cpp src\report_generator.cpp /Fe:dns_health_checker.exe
```

## Usage

```
DNS Health Checker - A tool to check DNS configuration health

Usage: dns_health_checker [options]

Options:
  -h, --help                 Show this help message
  -d, --domain DOMAIN        Specify domain to check
  -l, --local                Check local machine DNS settings
  -o, --output FILE          Specify output PDF file (default: dns_health_report.pdf)
  -s, --schedule MINUTES     Schedule recurring checks (in minutes)

Examples:
  dns_health_checker -d example.com
  dns_health_checker -l -o local_dns_report.pdf
  dns_health_checker -d example.com -s 60
```

## Security Checks

### SPF Check

- Verify existence of SPF record
- Check for proper syntax
- Validate authorized mail servers
- Identify overly permissive policies

### DKIM Check

- Check for DKIM selector records
- Verify proper key configuration
- Validate signature algorithm

### DMARC Check

- Verify existence of DMARC record
- Check policy settings (none/quarantine/reject)
- Validate reporting configuration

### MX Record Check

- Verify existence of MX records
- Check priority settings
- Validate mail server availability

### PTR/Reverse DNS Check

- Verify matching forward and reverse DNS
- Check for proper PTR record configuration
- Validate against common email delivery requirements

### DNSSEC Check

- Verify DNSSEC implementation
- Check for proper key signing
- Validate chain of trust

### TTL Evaluation

- Check for appropriate TTL values
- Identify values that are too low (causing excessive queries)
- Identify values that are too high (causing slow propagation)

## Current Implementation Status

### ✅ Completed Features

- Complete project structure with CMake build system
- Cross-platform command-line interface
- Comprehensive DNS check framework (SPF, DKIM, DMARC, MX, PTR, DNSSEC, TTL)
- PDF report generation (with libharu) or text fallback
- Unit testing framework
- Graceful handling of missing dependencies
- Windows build script for easy compilation

### ⚠️ Limitations

- **DNS Resolution**: Requires ldns library for actual DNS queries. Without it, checks will show "not applicable" status
- **PDF Generation**: Requires libharu library for PDF reports. Falls back to text reports if unavailable
- **Network Dependencies**: Some checks require internet connectivity to query external DNS servers

### 🔧 Building Without Dependencies

The application can be built and run without external dependencies, but with limited functionality:

- Domain validation still works
- Report structure and formatting work correctly
- All checks return appropriate "not applicable" status when DNS libraries are missing
- Text reports are generated instead of PDF when libharu is not available

This makes it easy to test the application structure and add DNS functionality later.

## Testing

Run the test suite to verify functionality:

```bash
# Build and run tests
cd build
make test_dns_checker  # If using CMake
./test_dns_checker

# Or compile manually
g++ -std=c++17 -I. -o test_dns_checker tests/test_dns_checker.cpp src/dns_health_checker.cpp src/dns_checker.cpp
./test_dns_checker
```

## Troubleshooting

### Common Issues

1. **"DNS functionality not available"**

   - Install ldns library: `sudo apt-get install libldns-dev` (Ubuntu) or `brew install ldns` (macOS)
   - Rebuild with `-DHAVE_LDNS=1` flag

2. **"PDF generation not available"**

   - Install libharu: `sudo apt-get install libhpdf-dev` (Ubuntu) or `brew install libharu` (macOS)
   - Rebuild with `-DHAVE_HPDF=1` flag

3. **Compilation errors**
   - Ensure C++17 compatible compiler
   - Check that all source files are present
   - Verify include paths are correct

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## Future Enhancements

- GUI interface using Qt or similar framework
- Batch domain checking from file input
- Email notifications for scheduled scans
- Integration with vulnerability databases
- Support for additional DNS record types (CAA, SRV, etc.)
- Historical trend analysis for scheduled scans
- REST API for integration with other tools
