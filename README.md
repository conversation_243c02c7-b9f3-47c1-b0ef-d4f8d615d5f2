# DNS Health Checker

A comprehensive cross-platform C++ application that performs DNS and domain health checks, generating detailed PDF reports to identify security vulnerabilities and misconfigurations.

## Features

### Domain & DNS Health Checks
- **SPF** (Sender Policy Framework) record verification
- **DKIM** (DomainKeys Identified Mail) record verification  
- **DMARC** (Domain-based Message Authentication, Reporting & Conformance) record verification
- **MX** (Mail Exchange) record validation
- **PTR/Reverse DNS** lookup verification
- **DNSSEC** (Domain Name System Security Extensions) availability checking
- **TTL** (Time To Live) evaluation for optimal caching

### Reporting
- Generate comprehensive PDF reports with detailed findings
- Include pass/fail status for each check
- Provide specific recommendations for fixing issues
- Support timestamped reports for scheduled scans

### Operational Modes
- One-time scan mode for immediate results
- Scheduled scan mode for periodic monitoring
- Support for both external domain and local machine checks

## Dependencies

- C++17 compatible compiler
- CMake 3.10+
- ldns library for DNS operations
- libharu (hpdf) for PDF generation
- Standard networking libraries

## Installation

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install build-essential cmake libldns-dev libhpdf-dev
```

### CentOS/RHEL/Fedora
```bash
sudo yum install gcc-c++ cmake ldns-devel libharu-devel
# or for newer versions:
sudo dnf install gcc-c++ cmake ldns-devel libharu-devel
```

### macOS
```bash
brew install cmake ldns libharu
```

### Windows
Install dependencies using vcpkg or manually build from source.

## Build Instructions

```bash
# Clone repository
git clone https://github.com/yourusername/dns-health-checker.git
cd dns-health-checker

# Build with CMake
mkdir build
cd build
cmake ..
make

# Run the application
./bin/dns_health_checker -d example.com
```

## Usage

```
DNS Health Checker - A tool to check DNS configuration health

Usage: dns_health_checker [options]

Options:
  -h, --help                 Show this help message
  -d, --domain DOMAIN        Specify domain to check
  -l, --local                Check local machine DNS settings
  -o, --output FILE          Specify output PDF file (default: dns_health_report.pdf)
  -s, --schedule MINUTES     Schedule recurring checks (in minutes)

Examples:
  dns_health_checker -d example.com
  dns_health_checker -l -o local_dns_report.pdf
  dns_health_checker -d example.com -s 60
```

## Security Checks

### SPF Check
- Verify existence of SPF record
- Check for proper syntax
- Validate authorized mail servers
- Identify overly permissive policies

### DKIM Check
- Check for DKIM selector records
- Verify proper key configuration
- Validate signature algorithm

### DMARC Check
- Verify existence of DMARC record
- Check policy settings (none/quarantine/reject)
- Validate reporting configuration

### MX Record Check
- Verify existence of MX records
- Check priority settings
- Validate mail server availability

### PTR/Reverse DNS Check
- Verify matching forward and reverse DNS
- Check for proper PTR record configuration
- Validate against common email delivery requirements

### DNSSEC Check
- Verify DNSSEC implementation
- Check for proper key signing
- Validate chain of trust

### TTL Evaluation
- Check for appropriate TTL values
- Identify values that are too low (causing excessive queries)
- Identify values that are too high (causing slow propagation)

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request
