cmake_minimum_required(VERSION 3.10)
project(dns-health-checker VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
if(NOT WIN32)
    find_package(PkgConfig REQUIRED)

    # Find ldns library
    pkg_check_modules(LDNS REQUIRED ldns)
else()
    # Windows: Try to find libraries manually or use vcpkg
    find_library(LDNS_LIBRARY
        NAMES ldns
        HINTS ${CMAKE_PREFIX_PATH}/lib
    )

    find_path(LDNS_INCLUDE_DIR
        NAMES ldns/ldns.h
        HINTS ${CMAKE_PREFIX_PATH}/include
    )

    if(LDNS_LIBRARY AND LDNS_INCLUDE_DIR)
        set(LDNS_LIBRARIES ${LDNS_LIBRARY})
        set(LDNS_INCLUDE_DIRS ${LDNS_INCLUDE_DIR})
        set(LDNS_FOUND TRUE)
    else()
        message(WARNING "ldns library not found. DNS functionality will be limited.")
        set(LDNS_FOUND FALSE)
    endif()
endif()

# Find libharu (hpdf) library
find_library(HPDF_LIBRARY
    NAMES hpdf libhpdf
    HINTS /usr/lib /usr/local/lib ${CMAKE_PREFIX_PATH}/lib
)

find_path(HPDF_INCLUDE_DIR
    NAMES hpdf.h
    HINTS /usr/include /usr/local/include ${CMAKE_PREFIX_PATH}/include
)

if(NOT HPDF_LIBRARY OR NOT HPDF_INCLUDE_DIR)
    message(WARNING "libharu (hpdf) library not found. PDF generation will be disabled.")
    set(HPDF_FOUND FALSE)
else()
    set(HPDF_FOUND TRUE)
endif()

# Include directories
if(LDNS_FOUND)
    include_directories(${LDNS_INCLUDE_DIRS})
endif()

if(HPDF_FOUND)
    include_directories(${HPDF_INCLUDE_DIR})
endif()

# Add executable
add_executable(dns_health_checker
    src/main.cpp
    src/dns_health_checker.cpp
    src/dns_checker.cpp
    src/report_generator.cpp
)

# Conditional compilation flags
if(LDNS_FOUND)
    target_compile_definitions(dns_health_checker PRIVATE HAVE_LDNS=1)
endif()

if(HPDF_FOUND)
    target_compile_definitions(dns_health_checker PRIVATE HAVE_HPDF=1)
endif()

# Link libraries
if(LDNS_FOUND)
    target_link_libraries(dns_health_checker ${LDNS_LIBRARIES})
endif()

if(HPDF_FOUND)
    target_link_libraries(dns_health_checker ${HPDF_LIBRARY})
endif()

# Compiler flags
if(LDNS_FOUND AND LDNS_CFLAGS_OTHER)
    target_compile_options(dns_health_checker PRIVATE ${LDNS_CFLAGS_OTHER})
endif()

# Platform-specific settings
if(WIN32)
    target_link_libraries(dns_health_checker ws2_32)
endif()

# Install target
install(TARGETS dns_health_checker DESTINATION bin)

# Enable testing
enable_testing()

# Add test executable
add_executable(test_dns_checker
    tests/test_dns_checker.cpp
    src/dns_health_checker.cpp
    src/dns_checker.cpp
)

target_link_libraries(test_dns_checker
    ${LDNS_LIBRARIES}
)

target_compile_options(test_dns_checker PRIVATE ${LDNS_CFLAGS_OTHER})

# Add test
add_test(NAME dns_checker_tests COMMAND test_dns_checker)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
