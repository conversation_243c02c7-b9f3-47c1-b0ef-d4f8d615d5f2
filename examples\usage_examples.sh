#!/bin/bash

# DNS Health Checker - Usage Examples
# This script demonstrates various ways to use the DNS Health Checker

echo "DNS Health Checker - Usage Examples"
echo "=================================="
echo

# Ensure the executable exists
if [ ! -f "../build/dns_health_checker" ] && [ ! -f "../dns_health_checker" ]; then
    echo "Error: dns_health_checker executable not found."
    echo "Please build the project first using the build instructions in README.md"
    exit 1
fi

# Find the executable
EXECUTABLE=""
if [ -f "../build/dns_health_checker" ]; then
    EXECUTABLE="../build/dns_health_checker"
elif [ -f "../dns_health_checker" ]; then
    EXECUTABLE="../dns_health_checker"
fi

echo "Using executable: $EXECUTABLE"
echo

# Example 1: Basic domain check
echo "Example 1: Basic domain health check"
echo "Command: $EXECUTABLE -d google.com"
echo "Description: Performs all DNS health checks for google.com"
echo
$EXECUTABLE -d google.com
echo
echo "----------------------------------------"
echo

# Example 2: Custom output file
echo "Example 2: Custom output file"
echo "Command: $EXECUTABLE -d example.com -o custom_report.pdf"
echo "Description: Saves report to custom_report.pdf (or .txt if PDF not available)"
echo
$EXECUTABLE -d example.com -o custom_report.pdf
echo
echo "----------------------------------------"
echo

# Example 3: Local machine check
echo "Example 3: Local machine DNS check"
echo "Command: $EXECUTABLE -l"
echo "Description: Checks local machine's DNS configuration"
echo
$EXECUTABLE -l
echo
echo "----------------------------------------"
echo

# Example 4: Help information
echo "Example 4: Display help information"
echo "Command: $EXECUTABLE --help"
echo "Description: Shows all available command-line options"
echo
$EXECUTABLE --help
echo
echo "----------------------------------------"
echo

# Example 5: Multiple domains (would require scripting)
echo "Example 5: Checking multiple domains (scripted approach)"
echo "Description: Check multiple domains in sequence"
echo

domains=("google.com" "github.com" "stackoverflow.com")

for domain in "${domains[@]}"; do
    echo "Checking $domain..."
    output_file="report_${domain//\./_}.pdf"
    $EXECUTABLE -d "$domain" -o "$output_file"
    echo "Report saved as: $output_file"
    echo
done

echo "----------------------------------------"
echo

echo "All examples completed!"
echo
echo "Notes:"
echo "- If DNS libraries (ldns) are not installed, checks will show 'not applicable'"
echo "- If PDF library (libharu) is not installed, text reports (.txt) will be generated"
echo "- For full functionality, install dependencies as described in README.md"
echo
echo "Generated reports can be found in the current directory."
