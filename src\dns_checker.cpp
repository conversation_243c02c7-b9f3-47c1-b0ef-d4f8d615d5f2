#include "dns_health_checker.h"
#ifdef HAVE_LDNS
#include <ldns/ldns.h>
#endif
#include <iostream>
#include <sstream>
#include <regex>
#include <algorithm>

namespace dns_health_checker
{

    CheckInfo DNSHealthChecker::checkSPF(const std::string &domain)
    {
        CheckInfo spf_check("SPF Record Check", CheckResult::FAIL);

        auto txt_records = queryTXT(domain);
        std::string spf_record;

        // Find SPF record
        for (const auto &record : txt_records)
        {
            if (record.find("v=spf1") == 0)
            {
                spf_record = record;
                break;
            }
        }

        if (spf_record.empty())
        {
            spf_check.details = "No SPF record found";
            spf_check.recommendations.push_back("Add an SPF record to prevent email spoofing");
            spf_check.recommendations.push_back("Example: 'v=spf1 include:_spf.google.com ~all'");
            return spf_check;
        }

        spf_check.result = CheckResult::PASS;
        spf_check.details = "SPF record found: " + spf_record;

        // Validate SPF syntax
        if (!validateSPFSyntax(spf_record))
        {
            spf_check.result = CheckResult::WARNING;
            spf_check.details += " (syntax issues detected)";
            spf_check.recommendations.push_back("Review SPF record syntax for errors");
        }

        // Check for overly permissive SPF
        if (isPermissiveSPF(spf_record))
        {
            spf_check.result = CheckResult::WARNING;
            spf_check.details += " (overly permissive)";
            spf_check.recommendations.push_back("Consider using '~all' or '-all' instead of '+all'");
        }

        return spf_check;
    }

    CheckInfo DNSHealthChecker::checkDKIM(const std::string &domain, const std::string &selector)
    {
        CheckInfo dkim_check("DKIM Record Check", CheckResult::FAIL);

        std::vector<std::string> selectors_to_check;
        if (selector != "default")
        {
            selectors_to_check.push_back(selector);
        }
        else
        {
            selectors_to_check = getCommonDKIMSelectors();
        }

        bool found_dkim = false;
        std::string found_selector;

        for (const auto &sel : selectors_to_check)
        {
            std::string dkim_domain = sel + "._domainkey." + domain;
            auto txt_records = queryTXT(dkim_domain);

            for (const auto &record : txt_records)
            {
                if (record.find("v=DKIM1") != std::string::npos || record.find("k=rsa") != std::string::npos)
                {
                    found_dkim = true;
                    found_selector = sel;
                    dkim_check.result = CheckResult::PASS;
                    dkim_check.details = "DKIM record found for selector '" + sel + "'";

                    if (!validateDKIMRecord(record))
                    {
                        dkim_check.result = CheckResult::WARNING;
                        dkim_check.details += " (potential issues detected)";
                        dkim_check.recommendations.push_back("Review DKIM record configuration");
                    }
                    break;
                }
            }

            if (found_dkim)
                break;
        }

        if (!found_dkim)
        {
            dkim_check.details = "No DKIM records found for common selectors";
            dkim_check.recommendations.push_back("Configure DKIM signing for email authentication");
            dkim_check.recommendations.push_back("Common selectors checked: default, google, selector1, selector2");
        }

        return dkim_check;
    }

    CheckInfo DNSHealthChecker::checkDMARC(const std::string &domain)
    {
        CheckInfo dmarc_check("DMARC Record Check", CheckResult::FAIL);

        std::string dmarc_domain = "_dmarc." + domain;
        auto txt_records = queryTXT(dmarc_domain);
        std::string dmarc_record;

        // Find DMARC record
        for (const auto &record : txt_records)
        {
            if (record.find("v=DMARC1") == 0)
            {
                dmarc_record = record;
                break;
            }
        }

        if (dmarc_record.empty())
        {
            dmarc_check.details = "No DMARC record found";
            dmarc_check.recommendations.push_back("Add a DMARC record to specify email authentication policy");
            dmarc_check.recommendations.push_back("Example: 'v=DMARC1; p=quarantine; rua=mailto:dmarc@" + domain + "'");
            return dmarc_check;
        }

        dmarc_check.result = CheckResult::PASS;
        dmarc_check.details = "DMARC record found: " + dmarc_record;

        // Validate DMARC syntax
        if (!validateDMARCSyntax(dmarc_record))
        {
            dmarc_check.result = CheckResult::WARNING;
            dmarc_check.details += " (syntax issues detected)";
            dmarc_check.recommendations.push_back("Review DMARC record syntax");
        }

        // Check policy strength
        std::string policy = extractDMARCPolicy(dmarc_record);
        if (policy == "none")
        {
            dmarc_check.result = CheckResult::WARNING;
            dmarc_check.details += " (policy set to 'none')";
            dmarc_check.recommendations.push_back("Consider upgrading policy to 'quarantine' or 'reject'");
        }

        return dmarc_check;
    }

    CheckInfo DNSHealthChecker::checkMX(const std::string &domain)
    {
        CheckInfo mx_check("MX Record Check", CheckResult::FAIL);

        auto mx_records = queryMX(domain);

        if (mx_records.empty())
        {
            mx_check.details = "No MX records found";
            mx_check.recommendations.push_back("Add MX records to enable email delivery");
            return mx_check;
        }

        mx_check.result = CheckResult::PASS;
        mx_check.details = "Found " + std::to_string(mx_records.size()) + " MX record(s)";

        // Validate MX priorities
        if (!validateMXPriorities(mx_records))
        {
            mx_check.result = CheckResult::WARNING;
            mx_check.details += " (priority issues detected)";
            mx_check.recommendations.push_back("Review MX record priorities for optimal mail routing");
        }

        return mx_check;
    }

    CheckInfo DNSHealthChecker::checkPTR(const std::string &domain)
    {
        CheckInfo ptr_check("PTR/Reverse DNS Check", CheckResult::NOT_APPLICABLE);

        // For domain checks, PTR is not directly applicable
        // This would be more relevant for IP address checks
        ptr_check.details = "PTR check not applicable for domain queries";
        ptr_check.recommendations.push_back("PTR records are checked for mail server IPs");

        return ptr_check;
    }

    CheckInfo DNSHealthChecker::checkDNSSEC(const std::string &domain)
    {
        CheckInfo dnssec_check("DNSSEC Check", CheckResult::FAIL);

        bool has_dnssec = queryDNSSEC(domain);

        if (has_dnssec)
        {
            dnssec_check.result = CheckResult::PASS;
            dnssec_check.details = "DNSSEC is enabled";
        }
        else
        {
            dnssec_check.details = "DNSSEC is not enabled";
            dnssec_check.recommendations.push_back("Enable DNSSEC to protect against DNS spoofing");
            dnssec_check.recommendations.push_back("Contact your DNS provider for DNSSEC setup");
        }

        return dnssec_check;
    }

    CheckInfo DNSHealthChecker::checkTTL(const std::string &domain)
    {
        CheckInfo ttl_check("TTL Check", CheckResult::PASS);

        uint32_t a_ttl = queryTTL(domain, "A");
        uint32_t mx_ttl = queryTTL(domain, "MX");

        std::stringstream details;
        details << "A record TTL: " << a_ttl << "s";
        if (mx_ttl > 0)
        {
            details << ", MX record TTL: " << mx_ttl << "s";
        }

        ttl_check.details = details.str();

        // Check if TTLs are optimal
        if (!isTTLOptimal(a_ttl, "A"))
        {
            ttl_check.result = CheckResult::WARNING;
            ttl_check.recommendations.push_back("Consider adjusting A record TTL (recommended: 300-3600 seconds)");
        }

        if (mx_ttl > 0 && !isTTLOptimal(mx_ttl, "MX"))
        {
            ttl_check.result = CheckResult::WARNING;
            ttl_check.recommendations.push_back("Consider adjusting MX record TTL (recommended: 3600-86400 seconds)");
        }

        return ttl_check;
    }

    // Helper function implementations
    bool DNSHealthChecker::validateSPFSyntax(const std::string &spf_record)
    {
        // Basic SPF syntax validation
        if (spf_record.find("v=spf1") != 0)
            return false;

        // Check for common syntax errors
        std::regex spf_regex(R"(v=spf1(\s+(include|a|mx|ip4|ip6|exists|redirect|exp):[^\s]+|\s+(all|\+all|-all|~all|\?all)|\s+[a-zA-Z0-9\-\.]+)*\s*)");
        return std::regex_match(spf_record, spf_regex);
    }

    bool DNSHealthChecker::isPermissiveSPF(const std::string &spf_record)
    {
        return spf_record.find("+all") != std::string::npos;
    }

    std::vector<std::string> DNSHealthChecker::getCommonDKIMSelectors()
    {
        return {"default", "google", "selector1", "selector2", "k1", "dkim", "mail"};
    }

    bool DNSHealthChecker::validateDKIMRecord(const std::string &dkim_record)
    {
        // Basic DKIM validation - check for required components
        return dkim_record.find("k=") != std::string::npos ||
               dkim_record.find("p=") != std::string::npos;
    }

    bool DNSHealthChecker::validateDMARCSyntax(const std::string &dmarc_record)
    {
        // Basic DMARC syntax validation
        return dmarc_record.find("v=DMARC1") == 0 &&
               dmarc_record.find("p=") != std::string::npos;
    }

    std::string DNSHealthChecker::extractDMARCPolicy(const std::string &dmarc_record)
    {
        std::regex policy_regex(R"(p=([^;]+))");
        std::smatch match;
        if (std::regex_search(dmarc_record, match, policy_regex))
        {
            return match[1].str();
        }
        return "unknown";
    }

    bool DNSHealthChecker::isTTLOptimal(uint32_t ttl, const std::string &record_type)
    {
        if (record_type == "A")
        {
            return ttl >= 300 && ttl <= 3600; // 5 minutes to 1 hour
        }
        else if (record_type == "MX")
        {
            return ttl >= 3600 && ttl <= 86400; // 1 hour to 1 day
        }
        return true; // Default to optimal for unknown types
    }

} // namespace dns_health_checker
