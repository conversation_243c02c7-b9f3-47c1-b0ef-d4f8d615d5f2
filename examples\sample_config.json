{"dns_health_checker": {"version": "1.0.0", "default_settings": {"output_format": "pdf", "output_directory": "./reports", "timeout_seconds": 30, "retry_attempts": 3}, "checks": {"spf": {"enabled": true, "warn_on_permissive": true, "required_mechanisms": ["include", "mx"]}, "dkim": {"enabled": true, "common_selectors": ["default", "google", "selector1", "selector2", "k1", "dkim", "mail"], "key_length_minimum": 1024}, "dmarc": {"enabled": true, "require_policy": true, "minimum_policy": "quarantine", "require_reporting": false}, "mx": {"enabled": true, "require_multiple": false, "check_connectivity": true}, "ptr": {"enabled": true, "check_reverse_dns": true}, "dnssec": {"enabled": true, "require_valid_chain": true}, "ttl": {"enabled": true, "a_record_min": 300, "a_record_max": 3600, "mx_record_min": 3600, "mx_record_max": 86400}}, "reporting": {"include_recommendations": true, "include_technical_details": true, "severity_levels": ["fail", "warning", "pass"], "custom_branding": {"company_name": "Your Company", "logo_path": "", "contact_email": "<EMAIL>"}}, "scheduling": {"enabled": false, "default_interval_minutes": 60, "max_concurrent_checks": 5, "notification_email": "", "notification_on_change_only": true}, "domains": {"example_domains": [{"domain": "example.com", "description": "Main company domain", "priority": "high", "custom_checks": {"dkim_selectors": ["selector1", "selector2"]}}, {"domain": "mail.example.com", "description": "Mail server domain", "priority": "critical", "custom_checks": {"require_ptr": true, "require_dnssec": true}}]}}}