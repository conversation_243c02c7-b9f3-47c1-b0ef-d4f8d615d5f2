#include "dns_health_checker.h"
#ifdef HAVE_HPDF
#include <hpdf.h>
#endif
#include <iostream>
#include <sstream>
#include <iomanip>
#include <fstream>

namespace dns_health_checker
{

    // Error handler for libharu
    void error_handler(HPDF_STATUS error_no, HPDF_STATUS detail_no, void *user_data)
    {
        std::cerr << "PDF Error: " << std::hex << error_no << ", detail: " << detail_no << std::endl;
    }

    bool generatePDFReport(const HealthReport &report, const std::string &output_file)
    {
#ifdef HAVE_HPDF
        HPDF_Doc pdf = HPDF_New(error_handler, nullptr);
        if (!pdf)
        {
            std::cerr << "Failed to create PDF document" << std::endl;
            return false;
        }

        try
        {
            // Set compression mode
            HPDF_SetCompressionMode(pdf, HPDF_COMP_ALL);

            // Add a page
            HPDF_Page page = HPDF_AddPage(pdf);
            HPDF_Page_SetSize(page, HPDF_PAGE_SIZE_A4, HPDF_PAGE_PORTRAIT);

            // Get page dimensions
            HPDF_REAL height = HPDF_Page_GetHeight(page);
            HPDF_REAL width = HPDF_Page_GetWidth(page);

            // Set up fonts
            HPDF_Font title_font = HPDF_GetFont(pdf, "Helvetica-Bold", nullptr);
            HPDF_Font header_font = HPDF_GetFont(pdf, "Helvetica-Bold", nullptr);
            HPDF_Font body_font = HPDF_GetFont(pdf, "Helvetica", nullptr);
            HPDF_Font mono_font = HPDF_GetFont(pdf, "Courier", nullptr);

            // Current Y position
            HPDF_REAL y_pos = height - 50;

            // Title
            HPDF_Page_BeginText(page);
            HPDF_Page_SetFontAndSize(page, title_font, 20);
            HPDF_Page_TextOut(page, 50, y_pos, "DNS Health Check Report");
            HPDF_Page_EndText(page);
            y_pos -= 40;

            // Domain and timestamp
            HPDF_Page_BeginText(page);
            HPDF_Page_SetFontAndSize(page, header_font, 14);
            std::string domain_text = "Domain: " + report.domain;
            HPDF_Page_TextOut(page, 50, y_pos, domain_text.c_str());
            HPDF_Page_EndText(page);
            y_pos -= 20;

            HPDF_Page_BeginText(page);
            HPDF_Page_SetFontAndSize(page, body_font, 12);
            std::string timestamp_text = "Generated: " + dns_utils::formatTimestamp(report.timestamp);
            HPDF_Page_TextOut(page, 50, y_pos, timestamp_text.c_str());
            HPDF_Page_EndText(page);
            y_pos -= 30;

            // Overall status
            HPDF_Page_BeginText(page);
            HPDF_Page_SetFontAndSize(page, header_font, 14);
            std::string status_text = "Overall Status: " + (report.overall_healthy ? "HEALTHY" : "ISSUES FOUND");
            HPDF_Page_TextOut(page, 50, y_pos, status_text.c_str());

            // Set color based on status
            if (report.overall_healthy)
            {
                HPDF_Page_SetRGBFill(page, 0.0, 0.6, 0.0); // Green
            }
            else
            {
                HPDF_Page_SetRGBFill(page, 0.8, 0.0, 0.0); // Red
            }
            HPDF_Page_EndText(page);
            y_pos -= 40;

            // Reset color to black
            HPDF_Page_SetRGBFill(page, 0.0, 0.0, 0.0);

            // Detailed checks
            HPDF_Page_BeginText(page);
            HPDF_Page_SetFontAndSize(page, header_font, 16);
            HPDF_Page_TextOut(page, 50, y_pos, "Detailed Check Results");
            HPDF_Page_EndText(page);
            y_pos -= 30;

            // Draw a line
            HPDF_Page_SetLineWidth(page, 1.0);
            HPDF_Page_MoveTo(page, 50, y_pos);
            HPDF_Page_LineTo(page, width - 50, y_pos);
            HPDF_Page_Stroke(page);
            y_pos -= 20;

            // Individual checks
            for (const auto &check : report.checks)
            {
                // Check if we need a new page
                if (y_pos < 100)
                {
                    page = HPDF_AddPage(pdf);
                    HPDF_Page_SetSize(page, HPDF_PAGE_SIZE_A4, HPDF_PAGE_PORTRAIT);
                    y_pos = height - 50;
                }

                // Check name
                HPDF_Page_BeginText(page);
                HPDF_Page_SetFontAndSize(page, header_font, 12);
                HPDF_Page_TextOut(page, 50, y_pos, check.name.c_str());
                HPDF_Page_EndText(page);

                // Result status
                HPDF_Page_BeginText(page);
                HPDF_Page_SetFontAndSize(page, header_font, 12);
                std::string result_text = dns_utils::resultToString(check.result);

                // Set color based on result
                switch (check.result)
                {
                case CheckResult::PASS:
                    HPDF_Page_SetRGBFill(page, 0.0, 0.6, 0.0); // Green
                    break;
                case CheckResult::FAIL:
                    HPDF_Page_SetRGBFill(page, 0.8, 0.0, 0.0); // Red
                    break;
                case CheckResult::WARNING:
                    HPDF_Page_SetRGBFill(page, 0.8, 0.6, 0.0); // Orange
                    break;
                case CheckResult::NOT_APPLICABLE:
                    HPDF_Page_SetRGBFill(page, 0.5, 0.5, 0.5); // Gray
                    break;
                }

                HPDF_Page_TextOut(page, 400, y_pos, result_text.c_str());
                HPDF_Page_EndText(page);
                y_pos -= 20;

                // Reset color to black
                HPDF_Page_SetRGBFill(page, 0.0, 0.0, 0.0);

                // Details
                if (!check.details.empty())
                {
                    HPDF_Page_BeginText(page);
                    HPDF_Page_SetFontAndSize(page, body_font, 10);

                    // Word wrap for long details
                    std::string details = check.details;
                    size_t max_line_length = 80;
                    size_t pos = 0;

                    while (pos < details.length())
                    {
                        size_t end_pos = std::min(pos + max_line_length, details.length());
                        if (end_pos < details.length())
                        {
                            // Find last space to avoid breaking words
                            size_t space_pos = details.find_last_of(' ', end_pos);
                            if (space_pos != std::string::npos && space_pos > pos)
                            {
                                end_pos = space_pos;
                            }
                        }

                        std::string line = details.substr(pos, end_pos - pos);
                        HPDF_Page_TextOut(page, 70, y_pos, line.c_str());
                        y_pos -= 12;
                        pos = end_pos + 1;

                        if (y_pos < 100)
                            break; // Prevent overflow
                    }

                    HPDF_Page_EndText(page);
                }

                // Recommendations
                if (!check.recommendations.empty())
                {
                    HPDF_Page_BeginText(page);
                    HPDF_Page_SetFontAndSize(page, body_font, 10);
                    HPDF_Page_SetRGBFill(page, 0.0, 0.0, 0.6); // Blue
                    HPDF_Page_TextOut(page, 70, y_pos, "Recommendations:");
                    HPDF_Page_EndText(page);
                    y_pos -= 15;

                    for (const auto &rec : check.recommendations)
                    {
                        if (y_pos < 100)
                            break; // Prevent overflow

                        HPDF_Page_BeginText(page);
                        HPDF_Page_SetFontAndSize(page, body_font, 9);
                        HPDF_Page_SetRGBFill(page, 0.0, 0.0, 0.0); // Black
                        std::string bullet = "• " + rec;
                        HPDF_Page_TextOut(page, 85, y_pos, bullet.c_str());
                        HPDF_Page_EndText(page);
                        y_pos -= 12;
                    }
                }

                y_pos -= 15; // Space between checks
            }

            // Save the document
            HPDF_SaveToFile(pdf, output_file.c_str());
        }
        catch (...)
        {
            HPDF_Free(pdf);
            return false;
        }

        // Clean up
        HPDF_Free(pdf);
        return true;
#else
        // Fallback: Generate text report
        std::ofstream file(output_file + ".txt");
        if (!file.is_open())
        {
            std::cerr << "Failed to create text report file" << std::endl;
            return false;
        }

        file << "DNS Health Check Report\n";
        file << "======================\n\n";
        file << "Domain: " << report.domain << "\n";
        file << "Generated: " << dns_utils::formatTimestamp(report.timestamp) << "\n";
        file << "Overall Status: " << (report.overall_healthy ? "HEALTHY" : "ISSUES FOUND") << "\n\n";

        file << "Detailed Check Results\n";
        file << "----------------------\n\n";

        for (const auto &check : report.checks)
        {
            file << check.name << ": " << dns_utils::resultToString(check.result) << "\n";
            if (!check.details.empty())
            {
                file << "  Details: " << check.details << "\n";
            }
            if (!check.recommendations.empty())
            {
                file << "  Recommendations:\n";
                for (const auto &rec : check.recommendations)
                {
                    file << "    • " << rec << "\n";
                }
            }
            file << "\n";
        }

        file.close();
        std::cout << "Note: PDF generation not available. Text report generated as " << output_file << ".txt" << std::endl;
        return true;
#endif
    }

} // namespace dns_health_checker
