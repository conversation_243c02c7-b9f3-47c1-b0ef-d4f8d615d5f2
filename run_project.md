# How to Run the DNS Health Checker Project

## Quick Start Guide

### Option 1: Using Visual Studio (Recommended for Windows)

1. **Install Visual Studio Community** (free):
   - Download from: https://visualstudio.microsoft.com/vs/community/
   - During installation, select "Desktop development with C++"

2. **Open Developer Command Prompt**:
   - Start Menu → Visual Studio 2022 → Developer Command Prompt for VS 2022

3. **Navigate to project directory**:
   ```cmd
   cd /d "d:\project"
   ```

4. **Compile the project**:
   ```cmd
   cl /EHsc /std:c++17 /I. src\main.cpp src\dns_health_checker.cpp src\dns_checker.cpp src\report_generator.cpp /Fe:dns_health_checker.exe
   ```

5. **Run the application**:
   ```cmd
   dns_health_checker.exe --help
   dns_health_checker.exe -d google.com
   ```

### Option 2: Using MinGW-w64 (GCC for Windows)

1. **Install MinGW-w64**:
   - Download from: https://www.mingw-w64.org/downloads/
   - Or use MSYS2: https://www.msys2.org/

2. **Add to PATH** (if not done automatically):
   - Add MinGW bin directory to your system PATH

3. **Compile**:
   ```cmd
   g++ -std=c++17 -I. -o dns_health_checker.exe src\main.cpp src\dns_health_checker.cpp src\dns_checker.cpp src\report_generator.cpp
   ```

4. **Run**:
   ```cmd
   dns_health_checker.exe --help
   ```

### Option 3: Using Online Compiler (For Testing)

If you don't want to install anything locally, you can test the code online:

1. **Go to an online C++ compiler**:
   - https://onlinegdb.com/online_c++_compiler
   - https://www.programiz.com/cpp-programming/online-compiler/
   - https://godbolt.org/

2. **Copy the source files** and combine them into a single file for testing

### Option 4: Using Docker (Cross-platform)

1. **Install Docker Desktop**

2. **Create a Dockerfile** in the project directory:
   ```dockerfile
   FROM gcc:latest
   WORKDIR /app
   COPY . .
   RUN g++ -std=c++17 -I. -o dns_health_checker src/main.cpp src/dns_health_checker.cpp src/dns_checker.cpp src/report_generator.cpp
   CMD ["./dns_health_checker", "--help"]
   ```

3. **Build and run**:
   ```cmd
   docker build -t dns-health-checker .
   docker run dns-health-checker
   ```

## What to Expect

### Without DNS Libraries (Current State)
When you run the application without ldns library installed, you'll see:

```
DNS Health Check Summary
Domain: google.com
Overall Status: ISSUES FOUND

SPF Record Check: NOT_APPLICABLE - DNS functionality not available
DKIM Record Check: NOT_APPLICABLE - DNS functionality not available
DMARC Record Check: NOT_APPLICABLE - DNS functionality not available
MX Record Check: NOT_APPLICABLE - DNS functionality not available
PTR/Reverse DNS Check: NOT_APPLICABLE - PTR check not applicable for domain queries
DNSSEC Check: NOT_APPLICABLE - DNS functionality not available
TTL Check: NOT_APPLICABLE - DNS functionality not available

Note: PDF generation not available. Text report generated as dns_health_report.pdf.txt
```

This is **expected behavior** and shows the application is working correctly!

### With Full Dependencies
If you install ldns and libharu libraries, you'll get actual DNS resolution and PDF reports.

## Testing the Application

### Basic Commands to Try

1. **Show help**:
   ```cmd
   dns_health_checker.exe --help
   ```

2. **Check a domain**:
   ```cmd
   dns_health_checker.exe -d google.com
   ```

3. **Check local machine**:
   ```cmd
   dns_health_checker.exe -l
   ```

4. **Custom output file**:
   ```cmd
   dns_health_checker.exe -d example.com -o my_report.pdf
   ```

### Run Tests

Compile and run the test suite:
```cmd
cl /EHsc /std:c++17 /I. tests\test_dns_checker.cpp src\dns_health_checker.cpp src\dns_checker.cpp /Fe:test_dns_checker.exe
test_dns_checker.exe
```

## Troubleshooting

### Common Issues

1. **"'cl' is not recognized"**
   - Install Visual Studio with C++ tools
   - Use Developer Command Prompt

2. **"'g++' is not recognized"**
   - Install MinGW-w64 or MSYS2
   - Add to system PATH

3. **Compilation errors**
   - Ensure C++17 support
   - Check all source files are present

### Next Steps for Full Functionality

To get actual DNS resolution working:

1. **Install vcpkg** (Microsoft's C++ package manager):
   ```cmd
   git clone https://github.com/Microsoft/vcpkg.git
   cd vcpkg
   .\bootstrap-vcpkg.bat
   ```

2. **Install dependencies**:
   ```cmd
   .\vcpkg install ldns:x64-windows
   .\vcpkg install libharu:x64-windows
   ```

3. **Rebuild with dependencies**:
   ```cmd
   cl /EHsc /std:c++17 /DHAVE_LDNS=1 /DHAVE_HPDF=1 /I. /I"vcpkg\installed\x64-windows\include" src\*.cpp /Fe:dns_health_checker.exe /link /LIBPATH:"vcpkg\installed\x64-windows\lib" ldns.lib hpdf.lib
   ```

## Success Indicators

✅ **Application compiles without errors**
✅ **Help message displays correctly**  
✅ **Domain validation works**
✅ **Reports are generated (even if text-only)**
✅ **Tests pass**

The application is working correctly even without external DNS libraries - it just operates in a "demo mode" where it validates the structure and shows what checks would be performed.
